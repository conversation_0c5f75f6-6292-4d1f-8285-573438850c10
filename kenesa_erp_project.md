# **مشروع كنيسة - نظام إدارة كنسي شامل على Odoo ERP**

## **نظرة عامة على المشروع**

### **الرؤية**
تطوير نظام إدارة كنسي شامل ومتطور باستخدام Odoo ERP يهدف إلى تسهيل وتنظيم جميع الأنشطة الكنسية والخدمية، مع الاستفادة من قوة وحدات Odoo الأساسية وإضافة وحدات مخصصة للاحتياجات الكنسية الخاصة.

### **الهدف**
إنشاء منصة متكاملة تجمع بين إدارة الأعضاء، الخدمات، الأحداث، المالية، والموارد البشرية في نظام واحد موحد يدعم اللغة العربية بالكامل ويوفر تجربة مستخدم متميزة.

### **المميزات الأساسية**
- **دعم كامل للغة العربية** مع إمكانية التبديل بين اللغات
- **تكامل كامل مع وحدات Odoo الأساسية** (CRM, HR, Accounting, Project, etc.)
- **واجهات مستخدم حديثة** مبنية على OWL Framework
- **تطبيقات محمولة** للأندرويد و iOS
- **أمان متقدم** مع تشفير البيانات والنسخ الاحتياطي التلقائي
- **تقارير وتحليلات ذكية** مع لوحات معلومات تفاعلية

---

## **تحليل النظام المرجعي (ChMeetings)**

### **الوحدات الأساسية المستخرجة**
1. **إدارة الأعضاء والأسر**
2. **إدارة الخدمات الكنسية**
3. **إدارة المجموعات**
4. **نظام المستخدمين والصلاحيات**
5. **إدارة الأحداث والاجتماعات**
6. **نظام الإفتقاد**
7. **الاستمارات والاستبيانات**
8. **المتابعة الروحية**
9. **التقويم الكنسي**
10. **التقارير والإحصائيات**

---

## **التصميم المعماري لـ Odoo**

### **الوحدات المطلوبة**

#### **1. الوحدة الأساسية (kenesa_base)**
```python
# الوحدات المعتمدة
'depends': ['base', 'mail', 'calendar', 'contacts', 'hr', 'crm', 'account', 'project']

# النماذج الأساسية
- kenesa.member (الأعضاء)
- kenesa.family (الأسر)
- kenesa.service (الخدمات)
- kenesa.group (المجموعات)
```

#### **2. وحدة إدارة الأعضاء (kenesa_members)**
```python
# النماذج
- kenesa.member (وراثة من res.partner)
- kenesa.family
- kenesa.member.spiritual.data
- kenesa.member.education
- kenesa.member.work
```

#### **3. وحدة الخدمات (kenesa_services)**
```python
# النماذج
- kenesa.service
- kenesa.service.type
- kenesa.service.member
- kenesa.service.schedule
```

#### **4. وحدة الأحداث (kenesa_events)**
```python
# النماذج (وراثة من calendar.event)
- kenesa.event
- kenesa.event.attendance
- kenesa.event.booking
- kenesa.event.agenda
```

#### **5. وحدة الإفتقاد (kenesa_pastoral_care)**
```python
# النماذج
- kenesa.pastoral.visit
- kenesa.pastoral.plan
- kenesa.pastoral.report
```

#### **6. وحدة المالية الكنسية (kenesa_finance)**
```python
# النماذج (تكامل مع account)
- kenesa.donation
- kenesa.tithe
- kenesa.expense.church
- kenesa.budget.service
```

---

## **النماذج والحقول التفصيلية**

### **نموذج العضو (kenesa.member)**
```python
class KenesaMember(models.Model):
    _name = 'kenesa.member'
    _inherit = ['res.partner', 'mail.thread', 'mail.activity.mixin']
    _description = 'Church Member'

    # البيانات الشخصية
    member_code = fields.Char('كود العضو', required=True)
    baptism_date = fields.Date('تاريخ المعمودية')
    confirmation_date = fields.Date('تاريخ التثبيت')
    spiritual_father = fields.Many2one('kenesa.priest', 'الأب الروحي')
    
    # البيانات العائلية
    family_id = fields.Many2one('kenesa.family', 'الأسرة')
    family_role = fields.Selection([
        ('father', 'الأب'),
        ('mother', 'الأم'),
        ('son', 'الابن'),
        ('daughter', 'الابنة')
    ], 'الدور في الأسرة')
    
    # البيانات الروحية
    confession_frequency = fields.Selection([
        ('weekly', 'أسبوعياً'),
        ('monthly', 'شهرياً'),
        ('quarterly', 'كل ثلاثة أشهر')
    ], 'معدل الاعتراف')
    
    # الخدمات والمجموعات
    service_ids = fields.Many2many('kenesa.service', 'الخدمات')
    group_ids = fields.Many2many('kenesa.group', 'المجموعات')
    
    # البيانات الإضافية
    qr_code = fields.Char('QR Code', compute='_compute_qr_code')
    attendance_rate = fields.Float('معدل الحضور', compute='_compute_attendance_rate')
```

### **نموذج الأسرة (kenesa.family)**
```python
class KenesaFamily(models.Model):
    _name = 'kenesa.family'
    _description = 'Church Family'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('اسم الأسرة', required=True)
    family_code = fields.Char('كود الأسرة')
    head_of_family = fields.Many2one('kenesa.member', 'رب الأسرة')
    member_ids = fields.One2many('kenesa.member', 'family_id', 'أفراد الأسرة')
    address = fields.Text('العنوان')
    district = fields.Char('المنطقة')
    pastoral_care_frequency = fields.Selection([
        ('monthly', 'شهرياً'),
        ('quarterly', 'كل ثلاثة أشهر'),
        ('yearly', 'سنوياً')
    ], 'معدل الإفتقاد')
```

---

## **الميزات الإضافية المقترحة**

### **1. الذكاء الاصطناعي والتحليلات**
- **تحليل أنماط الحضور** باستخدام Machine Learning
- **توقع احتياجات الأعضاء** بناءً على البيانات التاريخية
- **اقتراحات تلقائية للإفتقاد** بناءً على الأولويات
- **تحليل المشاعر** في الاستبيانات والتقييمات

### **2. التكامل مع الخدمات الخارجية**
- **خدمات الدفع الإلكتروني** (فوري، فودافون كاش، إلخ)
- **خدمات الرسائل النصية** المحلية
- **تكامل مع وسائل التواصل الاجتماعي**
- **خدمات البث المباشر** للقداسات والاجتماعات

### **3. تطبيقات الهاتف المحمول**
- **تطبيق للأعضاء** (حضور، تبرعات، تواصل)
- **تطبيق للخدام** (إدارة الخدمة، الإفتقاد)
- **تطبيق للكهنة** (المتابعة الروحية، الاعترافات)

### **4. ميزات الأمان المتقدمة**
- **تشفير البيانات الحساسة**
- **مصادقة ثنائية العامل**
- **سجل مراجعة شامل**
- **نسخ احتياطي تلقائي مشفر**

### **5. إدارة الموارد الكنسية**
- **إدارة المباني والقاعات**
- **جدولة استخدام الموارد**
- **صيانة المعدات**
- **إدارة المكتبة الكنسية**

---

## **واجهات المستخدم المقترحة**

### **1. لوحة المعلومات الرئيسية**
- **إحصائيات سريعة** (عدد الأعضاء، الحضور، التبرعات)
- **الأحداث القادمة**
- **تنبيهات الإفتقاد**
- **رسوم بيانية تفاعلية**

### **2. شاشة إدارة الأعضاء**
- **بحث متقدم** مع فلاتر متعددة
- **عرض شجري للأسر**
- **خرائط توزيع جغرافي**
- **تصدير واستيراد البيانات**

### **3. شاشة الأحداث والاجتماعات**
- **تقويم تفاعلي**
- **نظام حجوزات**
- **تسجيل حضور سريع**
- **إدارة الأجندة**

---

## **التقارير والتحليلات**

### **1. تقارير الأعضاء**
- تقرير الأعضاء الجدد
- تقرير أعياد الميلاد
- تقرير التوزيع الجغرافي
- تقرير الحالة الروحية

### **2. تقارير الحضور**
- تقرير الحضور الشهري
- تقرير معدلات الحضور
- تقرير الغياب المتكرر
- تقرير حضور الخدمات

### **3. تقارير مالية**
- تقرير التبرعات
- تقرير العشور
- تقرير المصروفات
- تقرير الميزانية

---

## **خطة التطوير**

### **المرحلة الأولى (3 أشهر)**
1. إعداد البيئة التطويرية
2. تطوير الوحدة الأساسية
3. تطوير وحدة إدارة الأعضاء
4. تطوير واجهات المستخدم الأساسية

### **المرحلة الثانية (3 أشهر)**
1. تطوير وحدة الخدمات
2. تطوير وحدة الأحداث
3. تطوير نظام التقارير الأساسية
4. اختبارات الوحدة

### **المرحلة الثالثة (3 أشهر)**
1. تطوير وحدة الإفتقاد
2. تطوير وحدة المالية
3. تطوير التطبيقات المحمولة
4. اختبارات التكامل

### **المرحلة الرابعة (3 أشهر)**
1. تطوير الميزات المتقدمة
2. تحسين الأداء والأمان
3. اختبارات شاملة
4. التوثيق والتدريب

---

## **الاعتبارات التقنية**

### **متطلبات النظام**
- **Odoo 18 Community/Enterprise**
- **Python 3.10+**
- **PostgreSQL 13+**
- **Redis** للتخزين المؤقت
- **Nginx** كخادم ويب

### **الأمان والخصوصية**
- تطبيق معايير GDPR
- تشفير البيانات الحساسة
- مراجعة دورية للأمان
- نسخ احتياطي مشفر

### **الأداء والتوسع**
- تحسين استعلامات قاعدة البيانات
- استخدام التخزين المؤقت
- تحسين الصور والملفات
- دعم التوسع الأفقي

---

## **الخلاصة**

هذا المشروع يهدف إلى إنشاء نظام إدارة كنسي شامل ومتطور يجمع بين قوة Odoo ERP والاحتياجات الخاصة للكنائس. النظام سيوفر حلولاً متكاملة لإدارة الأعضاء، الخدمات، الأحداث، والمالية مع التركيز على سهولة الاستخدام والأمان والأداء العالي.

---

## **ملاحق إضافية**

### **أ. قائمة الحقول المخصصة للأعضاء**

#### **البيانات الشخصية المتقدمة**
```python
# حقول إضافية لنموذج العضو
blood_type = fields.Selection([
    ('A+', 'A+'), ('A-', 'A-'),
    ('B+', 'B+'), ('B-', 'B-'),
    ('AB+', 'AB+'), ('AB-', 'AB-'),
    ('O+', 'O+'), ('O-', 'O-')
], 'فصيلة الدم')

medical_conditions = fields.Text('الحالات الطبية')
emergency_contact = fields.Char('جهة الاتصال في الطوارئ')
emergency_phone = fields.Char('هاتف الطوارئ')
national_id = fields.Char('الرقم القومي')
passport_number = fields.Char('رقم جواز السفر')
```

#### **البيانات التعليمية والمهنية**
```python
education_level = fields.Selection([
    ('primary', 'ابتدائي'),
    ('preparatory', 'إعدادي'),
    ('secondary', 'ثانوي'),
    ('university', 'جامعي'),
    ('postgraduate', 'دراسات عليا')
], 'المستوى التعليمي')

university = fields.Char('الجامعة')
faculty = fields.Char('الكلية')
graduation_year = fields.Integer('سنة التخرج')
profession = fields.Char('المهنة')
workplace = fields.Char('مكان العمل')
work_phone = fields.Char('هاتف العمل')
```

#### **البيانات الروحية المتقدمة**
```python
baptism_church = fields.Char('كنيسة المعمودية')
confirmation_church = fields.Char('كنيسة التثبيت')
last_confession_date = fields.Date('تاريخ آخر اعتراف')
communion_frequency = fields.Selection([
    ('weekly', 'أسبوعياً'),
    ('monthly', 'شهرياً'),
    ('occasionally', 'أحياناً')
], 'معدل التناول')

spiritual_gifts = fields.Many2many('kenesa.spiritual.gift', 'المواهب الروحية')
ministry_interests = fields.Many2many('kenesa.ministry.type', 'اهتمامات الخدمة')
```

### **ب. نماذج إضافية مقترحة**

#### **1. نموذج الكاهن (kenesa.priest)**
```python
class KenesaPriest(models.Model):
    _name = 'kenesa.priest'
    _inherit = ['kenesa.member']
    _description = 'Church Priest'

    ordination_date = fields.Date('تاريخ الرسامة', required=True)
    rank = fields.Selection([
        ('deacon', 'شماس'),
        ('priest', 'قس'),
        ('hegumen', 'قمص'),
        ('bishop', 'أسقف')
    ], 'الرتبة الكهنوتية')

    assigned_services = fields.Many2many('kenesa.service', 'الخدمات المسؤول عنها')
    confession_schedule = fields.One2many('kenesa.confession.schedule', 'priest_id', 'جدول الاعترافات')
```

#### **2. نموذج التبرعات (kenesa.donation)**
```python
class KenesaDonation(models.Model):
    _name = 'kenesa.donation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Church Donation'

    member_id = fields.Many2one('kenesa.member', 'المتبرع', required=True)
    amount = fields.Monetary('المبلغ', required=True)
    currency_id = fields.Many2one('res.currency', 'العملة', default=lambda self: self.env.company.currency_id)
    donation_type = fields.Selection([
        ('tithe', 'عشور'),
        ('offering', 'تقدمة'),
        ('special', 'تبرع خاص'),
        ('building', 'بناء'),
        ('charity', 'أعمال خير')
    ], 'نوع التبرع', required=True)

    date = fields.Date('التاريخ', default=fields.Date.today)
    payment_method = fields.Selection([
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('online', 'دفع إلكتروني')
    ], 'طريقة الدفع')

    receipt_number = fields.Char('رقم الإيصال')
    notes = fields.Text('ملاحظات')
    is_anonymous = fields.Boolean('تبرع مجهول')
```

#### **3. نموذج المكتبة الكنسية (kenesa.library)**
```python
class KenesaLibraryBook(models.Model):
    _name = 'kenesa.library.book'
    _description = 'Church Library Book'

    name = fields.Char('عنوان الكتاب', required=True)
    author = fields.Char('المؤلف')
    isbn = fields.Char('ISBN')
    category_id = fields.Many2one('kenesa.book.category', 'التصنيف')
    language = fields.Selection([
        ('ar', 'عربي'),
        ('en', 'إنجليزي'),
        ('fr', 'فرنسي')
    ], 'اللغة')

    copies_total = fields.Integer('إجمالي النسخ')
    copies_available = fields.Integer('النسخ المتاحة', compute='_compute_available_copies')
    location = fields.Char('موقع الكتاب')

    borrowing_ids = fields.One2many('kenesa.book.borrowing', 'book_id', 'سجل الاستعارة')
```

### **ج. تكامل مع وحدات Odoo الأساسية**

#### **1. تكامل مع وحدة CRM**
- **تحويل الزوار إلى أعضاء** تلقائياً
- **متابعة الأعضاء الجدد** من خلال pipeline مخصص
- **إدارة الأنشطة التبشيرية**

#### **2. تكامل مع وحدة HR**
- **إدارة الخدام كموظفين**
- **تتبع ساعات الخدمة**
- **تقييم أداء الخدام**
- **إدارة التدريبات والدورات**

#### **3. تكامل مع وحدة المحاسبة**
- **ربط التبرعات بالحسابات المالية**
- **إنشاء فواتير للخدمات المدفوعة**
- **تتبع المصروفات الكنسية**
- **إعداد الميزانية السنوية**

#### **4. تكامل مع وحدة المشاريع**
- **إدارة المشاريع الكنسية**
- **تتبع مراحل البناء والتطوير**
- **إدارة فرق العمل**
- **متابعة الميزانيات والمواعيد**

### **د. ميزات الأمان المتقدمة**

#### **1. إدارة الصلاحيات المتقدمة**
```python
# مجموعات الصلاحيات المقترحة
- kenesa_admin: إدارة كاملة للنظام
- kenesa_priest: صلاحيات الكهنة
- kenesa_deacon: صلاحيات الشمامسة
- kenesa_servant: صلاحيات الخدام
- kenesa_member: صلاحيات الأعضاء العادية
- kenesa_visitor: صلاحيات الزوار
```

#### **2. تشفير البيانات الحساسة**
```python
# حقول مشفرة
encrypted_national_id = fields.Char('الرقم القومي المشفر')
encrypted_phone = fields.Char('الهاتف المشفر')
encrypted_address = fields.Text('العنوان المشفر')
```

#### **3. سجل المراجعة**
```python
class KenesaAuditLog(models.Model):
    _name = 'kenesa.audit.log'
    _description = 'Audit Log'

    user_id = fields.Many2one('res.users', 'المستخدم')
    model_name = fields.Char('النموذج')
    record_id = fields.Integer('معرف السجل')
    action = fields.Selection([
        ('create', 'إنشاء'),
        ('write', 'تعديل'),
        ('unlink', 'حذف')
    ], 'الإجراء')
    old_values = fields.Text('القيم القديمة')
    new_values = fields.Text('القيم الجديدة')
    timestamp = fields.Datetime('الوقت', default=fields.Datetime.now)
```

### **هـ. التطبيقات المحمولة**

#### **1. تطبيق الأعضاء**
**الميزات الأساسية:**
- تسجيل الدخول الآمن
- عرض البيانات الشخصية
- تحديث معلومات الاتصال
- عرض جدول الأحداث
- تسجيل الحضور عبر QR Code
- التبرع الإلكتروني
- استقبال الإشعارات

#### **2. تطبيق الخدام**
**الميزات الأساسية:**
- إدارة المجموعات المسؤول عنها
- تسجيل حضور الأعضاء
- إضافة زيارات الإفتقاد
- عرض التقارير السريعة
- إرسال رسائل للأعضاء
- جدولة الأنشطة

#### **3. تطبيق الكهنة**
**الميزات الأساسية:**
- إدارة جدول الاعترافات
- متابعة الحالة الروحية للأعضاء
- إدارة المناسبات الكنسية
- عرض الإحصائيات الشاملة
- إدارة الصلاحيات

### **و. خطة التسويق والانتشار**

#### **1. المرحلة التجريبية**
- اختيار 3-5 كنائس للتجربة
- تدريب الفرق المحلية
- جمع التغذية الراجعة
- تحسين النظام

#### **2. مرحلة الإطلاق**
- إطلاق النسخة الأولى
- حملة تسويقية مستهدفة
- ورش عمل تدريبية
- دعم فني مكثف

#### **3. مرحلة التوسع**
- إضافة ميزات جديدة
- دعم كنائس أكبر
- تطوير شراكات
- توسيع الفريق التقني

---

## **الخاتمة والتوصيات**

يمثل هذا المشروع فرصة ذهبية لتطوير نظام إدارة كنسي متطور يجمع بين التقنيات الحديثة والاحتياجات الروحية والإدارية للكنائس. النجاح في هذا المشروع يتطلب:

1. **فريق تطوير متخصص** في Odoo والتطبيقات الكنسية
2. **تعاون وثيق مع الكنائس** لفهم الاحتياجات الفعلية
3. **استثمار في الأمان والخصوصية** لحماية البيانات الحساسة
4. **خطة تدريب شاملة** للمستخدمين النهائيين
5. **دعم فني مستمر** لضمان نجاح التطبيق

مع التخطيط الجيد والتنفيذ المتقن، يمكن لهذا النظام أن يحدث نقلة نوعية في إدارة الكنائس ويساهم في تطوير الخدمة الكنسية بشكل عام.
